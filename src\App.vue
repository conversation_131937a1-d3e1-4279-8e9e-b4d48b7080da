<script>
import store from '@/stores/index.js'

export default {
  onLaunch: function () {
    console.log('燃气管理系统启动')

    // 初始化store
    store.init()

    // 检查登录状态
    this.checkLoginStatus()

    // 获取设备信息
    this.getDeviceInfo()
  },

  onShow: function () {
    console.log('App Show')
  },

  onHide: function () {
    console.log('App Hide')
  },

  methods: {
    // 检查登录状态
    checkLoginStatus() {
      const userInfo = uni.getStorageSync('userInfo')
      if (!userInfo || !userInfo.isLoggedIn) {
        // 未登录，跳转到登录页
        uni.reLaunch({
          url: '/pages/login/login'
        })
      } else {
        // 已登录，确保在首页
        const pages = getCurrentPages()
        if (pages.length === 0) {
          uni.reLaunch({
            url: '/pages/dashboard/dashboard'
          })
        }
      }
    },

    // 获取设备信息
    getDeviceInfo() {
      uni.getSystemInfo({
        success: (res) => {
          console.log('设备信息:', res)
          // 可以将设备信息存储到store中
        }
      })
    }
  }
}
</script>

<style lang="scss">
/* 每个页面公共css */
page {
  background-color: #f5f5f5;
}

/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, $uni-color-primary 0%, $uni-color-primary-dark 100%);
  color: white;
  padding: 0 $uni-spacing-base;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: $navbar-height;

  .navbar-title {
    font-size: $uni-font-size-lg;
    font-weight: 500;
  }

  .navbar-back {
    width: 44rpx;
    height: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* 底部安全区域适配 */
.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
