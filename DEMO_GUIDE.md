# 燃气管理系统 - 演示指南

## 🎯 快速体验

### 登录信息
- **手机号**: 任意11位手机号（如：13800138000）
- **验证码**: `123456`（固定验证码）
- **角色**: 可选择维保员、巡检员或配送员

### 体验流程

#### 1. 登录系统
1. 打开应用，进入登录页面
2. 输入任意11位手机号
3. 点击"获取验证码"
4. 输入验证码：`123456`
5. 选择角色（推荐选择"维保员"体验完整功能）
6. 勾选用户协议
7. 点击"立即登录"

#### 2. 工作台首页
登录成功后会进入工作台首页，可以看到：
- 个性化问候语
- 今日数据概览（任务数量、工作时长等）
- 快速操作入口
- 最新任务列表
- 今日工作安排

#### 3. 任务管理
点击底部"任务"标签，体验任务管理功能：
- 查看任务列表（包含20条模拟任务）
- 使用搜索功能查找任务
- 通过状态、类型、优先级筛选任务
- 点击任务查看详细信息
- 体验任务接收、开始、完成流程

#### 4. 现场执行
点击底部"执行"标签，体验现场作业功能：
- 查看当前位置信息
- 使用快速操作（扫码、拍照、记录、测量）
- 查看工作流程指导
- 使用工具箱（计算器、计时器、手电筒等）
- 查看最近工作记录

#### 5. 个人中心
点击底部"我的"标签，体验个人中心功能：
- 查看个人信息
- 签到/签退操作
- 查看月度工作统计
- 浏览功能菜单
- **UI演示**: 点击"UI演示"查看所有组件效果
- 修改个人设置

#### 6. 知识库
点击底部"知识库"标签，体验知识管理功能：
- 浏览技术文档
- 使用搜索功能
- 查看推荐内容
- 收藏/分享文档
- 按分类浏览内容

## 📱 功能亮点

### 🎨 简约美观设计
- **主题色**: #4c6ef5 紫蓝色主题，简约现代
- **简约卡片**: 轻量级阴影，12rpx圆角，简洁优雅
- **扁平色彩**: 去除复杂渐变，使用纯色设计
- **微动画**: 轻微的悬停效果，不过度炫技
- **清爽布局**: 适当留白，视觉层次清晰
- **UI演示页面**: 可在"我的" → "UI演示"中查看所有组件效果

### 📋 完整工作流
- 任务接收 → 现场执行 → 完成确认
- 工作记录 → 数据统计 → 知识积累
- 签到签退 → 工时统计 → 绩效评估

### 🔧 实用工具
- GPS定位导航
- 二维码扫描
- 拍照记录
- 电子签名
- 工具箱集成

### 📚 知识管理
- 技术手册
- 操作指南
- 常见问题
- 视频教程

## 🎮 模拟数据说明

### 任务数据
- 自动生成20条不同类型的任务
- 包含维护、巡检、配送、维修等类型
- 随机分配状态、优先级、地址等信息
- 支持搜索和筛选功能

### 用户数据
- 模拟用户信息：张三（工号：GS001）
- 包含工作统计、评分、签到记录等
- 支持签到签退操作

### 消息数据
- 自动生成20条不同类型的消息
- 包含任务通知、系统消息、公告等
- 支持已读/未读状态管理

### 知识库数据
- 自动生成30篇技术文档
- 包含不同分类和类型
- 支持搜索、收藏、分享功能

## 🔄 数据刷新

所有模拟数据都是动态生成的，每次重新加载页面都会生成新的数据。如需查看不同的数据效果，可以：

1. 下拉刷新页面
2. 重新进入页面
3. 使用不同的筛选条件

## 📝 开发说明

### API模拟
- 所有API调用都使用模拟数据
- 模拟了真实的网络延迟（500-2000ms）
- 包含成功和失败的响应处理

### 状态管理
- 使用自定义状态管理系统
- 支持用户登录状态持久化
- 实现了完整的数据流管理

### 响应式设计
- 适配不同屏幕尺寸
- 支持横竖屏切换
- 优化了触摸交互体验

## 🚀 下一步开发

1. **后端集成**: 替换模拟API为真实后端接口
2. **功能完善**: 添加更多业务功能和工具
3. **性能优化**: 优化加载速度和内存使用
4. **测试完善**: 添加单元测试和集成测试
5. **部署发布**: 配置生产环境并发布应用

## 💡 使用建议

1. **完整体验**: 建议按照登录→任务→执行→知识库的顺序体验
2. **功能测试**: 尝试所有的交互功能，包括搜索、筛选、操作等
3. **界面适配**: 在不同设备和屏幕尺寸下测试界面效果
4. **性能观察**: 注意页面加载速度和交互响应时间

---

🎉 **开始您的燃气管理系统体验之旅吧！**
