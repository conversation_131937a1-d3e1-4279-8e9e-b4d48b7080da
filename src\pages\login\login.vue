<template>
  <view class="login-container">
    <!-- 自定义状态栏 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 头部区域 -->
    <view class="header">
      <view class="logo-section">
        <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
        <text class="app-name">燃气管理系统</text>
        <text class="app-desc">专业的燃气设备维护管理平台</text>
      </view>
    </view>
    
    <!-- 登录表单 -->
    <view class="form-container">
      <view class="form-card">
        <view class="form-title">欢迎登录</view>
        
        <!-- 手机号输入 -->
        <view class="form-item">
          <view class="input-wrapper">
            <text class="input-icon">📱</text>
            <input 
              class="input-field"
              type="number"
              placeholder="请输入手机号"
              v-model="form.phone"
              maxlength="11"
              @input="onPhoneInput"
            />
          </view>
          <text v-if="errors.phone" class="error-text">{{ errors.phone }}</text>
        </view>
        
        <!-- 验证码输入 -->
        <view class="form-item">
          <view class="input-wrapper">
            <text class="input-icon">🔐</text>
            <input 
              class="input-field"
              type="number"
              placeholder="请输入验证码"
              v-model="form.code"
              maxlength="6"
            />
            <button 
              class="code-btn"
              :class="{ disabled: !canSendCode || countdown > 0 }"
              @click="sendCode"
              :disabled="!canSendCode || countdown > 0"
            >
              {{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
            </button>
          </view>
          <text v-if="errors.code" class="error-text">{{ errors.code }}</text>
        </view>
        
        <!-- 角色选择 -->
        <view class="form-item">
          <view class="role-title">选择角色</view>
          <view class="role-options">
            <view 
              class="role-option"
              :class="{ active: form.role === role.value }"
              v-for="role in roles"
              :key="role.value"
              @click="selectRole(role.value)"
            >
              <text class="role-icon">{{ role.icon }}</text>
              <text class="role-name">{{ role.name }}</text>
            </view>
          </view>
          <text v-if="errors.role" class="error-text">{{ errors.role }}</text>
        </view>
        
        <!-- 登录按钮 -->
        <button 
          class="login-btn"
          :class="{ loading: isLoading }"
          @click="handleLogin"
          :disabled="isLoading"
        >
          <text v-if="isLoading">登录中...</text>
          <text v-else>立即登录</text>
        </button>
        
        <!-- 协议条款 -->
        <view class="agreement">
          <checkbox-group @change="onAgreementChange">
            <label class="agreement-item">
              <checkbox value="agree" :checked="form.agreement" />
              <text class="agreement-text">
                我已阅读并同意
                <text class="link" @click="showAgreement">《用户协议》</text>
                和
                <text class="link" @click="showPrivacy">《隐私政策》</text>
              </text>
            </label>
          </checkbox-group>
        </view>
      </view>
    </view>
    
    <!-- 底部信息 -->
    <view class="footer">
      <text class="version">版本 v1.0.0</text>
      <text class="company">© 2024 燃气管理系统</text>
    </view>
  </view>
</template>

<script>
import store from '@/stores/index.js'
import { userApi } from '@/api/index.js'
import { validatePhone, showToast, showLoading, hideLoading } from '@/utils/index.js'

export default {
  data() {
    return {
      statusBarHeight: 0,
      form: {
        phone: '',
        code: '',
        role: '',
        agreement: false
      },
      errors: {},
      isLoading: false,
      countdown: 0,
      timer: null,
      roles: [
        { value: 'maintenance', name: '维保员', icon: '🔧' },
        { value: 'inspector', name: '巡检员', icon: '🔍' },
        { value: 'delivery', name: '配送员', icon: '🚚' }
      ]
    }
  },
  
  computed: {
    canSendCode() {
      return validatePhone(this.form.phone)
    }
  },
  
  onLoad() {
    this.getSystemInfo()
  },
  
  onUnload() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  
  methods: {
    // 获取系统信息
    getSystemInfo() {
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight
        }
      })
    },
    
    // 手机号输入处理
    onPhoneInput() {
      if (this.errors.phone) {
        this.errors.phone = ''
      }
    },
    
    // 选择角色
    selectRole(role) {
      this.form.role = role
      if (this.errors.role) {
        this.errors.role = ''
      }
    },
    
    // 发送验证码
    async sendCode() {
      if (!this.canSendCode) {
        showToast('请输入正确的手机号')
        return
      }
      
      try {
        showLoading('发送中...')
        await userApi.sendSms(this.form.phone)
        hideLoading()
        showToast('验证码已发送')
        this.startCountdown()
      } catch (error) {
        hideLoading()
        showToast(error.message || '发送失败')
      }
    },
    
    // 开始倒计时
    startCountdown() {
      this.countdown = 60
      this.timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.timer)
          this.timer = null
        }
      }, 1000)
    },
    
    // 表单验证
    validateForm() {
      this.errors = {}
      
      if (!this.form.phone) {
        this.errors.phone = '请输入手机号'
      } else if (!validatePhone(this.form.phone)) {
        this.errors.phone = '请输入正确的手机号'
      }
      
      if (!this.form.code) {
        this.errors.code = '请输入验证码'
      } else if (this.form.code.length !== 6) {
        this.errors.code = '验证码格式不正确'
      }
      
      if (!this.form.role) {
        this.errors.role = '请选择角色'
      }
      
      if (!this.form.agreement) {
        showToast('请先同意用户协议和隐私政策')
        return false
      }
      
      return Object.keys(this.errors).length === 0
    },
    
    // 登录处理
    async handleLogin() {
      if (!this.validateForm()) {
        return
      }
      
      this.isLoading = true
      
      try {
        const result = await userApi.login({
          phone: this.form.phone,
          code: this.form.code,
          role: this.form.role
        })
        
        // 保存token
        uni.setStorageSync('token', result.token)

        // 保存用户信息到store
        store.login({
          id: result.userInfo.id,
          name: result.userInfo.name,
          phone: result.userInfo.phone,
          role: result.userInfo.role,
          avatar: result.userInfo.avatar,
          workNumber: result.userInfo.workNumber
        })
        
        showToast('登录成功')
        
        // 跳转到主页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/dashboard/dashboard'
          })
        }, 1000)
        
      } catch (error) {
        showToast(error.message || '登录失败')
      } finally {
        this.isLoading = false
      }
    },
    
    // 协议变更
    onAgreementChange(e) {
      this.form.agreement = e.detail.value.includes('agree')
    },
    
    // 显示用户协议
    showAgreement() {
      uni.showModal({
        title: '用户协议',
        content: '这里是用户协议内容...',
        showCancel: false
      })
    },
    
    // 显示隐私政策
    showPrivacy() {
      uni.showModal({
        title: '隐私政策',
        content: '这里是隐私政策内容...',
        showCancel: false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.status-bar {
  background: transparent;
}

.header {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $uni-spacing-2xl 0;
}

.logo-section {
  text-align: center;
  color: white;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: $uni-spacing-lg;
}

.app-name {
  display: block;
  font-size: $uni-font-size-2xl;
  font-weight: bold;
  margin-bottom: $uni-spacing-sm;
}

.app-desc {
  display: block;
  font-size: $uni-font-size-base;
  opacity: 0.8;
}

.form-container {
  padding: 0 $uni-spacing-lg;
  margin-bottom: $uni-spacing-2xl;
}

.form-card {
  background: white;
  border-radius: $uni-border-radius-xl;
  padding: $uni-spacing-2xl $uni-spacing-lg;
  box-shadow: $shadow-lg;
}

.form-title {
  font-size: $uni-font-size-xl;
  font-weight: bold;
  text-align: center;
  margin-bottom: $uni-spacing-2xl;
  color: $uni-text-color;
}

.form-item {
  margin-bottom: $uni-spacing-lg;
}

.input-wrapper {
  display: flex;
  align-items: center;
  border: 1rpx solid $uni-border-color;
  border-radius: $uni-border-radius-base;
  background: #f8f9fa;
  overflow: hidden;
}

.input-icon {
  padding: 0 $uni-spacing-base;
  font-size: $uni-font-size-lg;
}

.input-field {
  flex: 1;
  height: $input-height;
  padding: 0 $uni-spacing-base;
  font-size: $uni-font-size-base;
  border: none;
  background: transparent;
}

.code-btn {
  padding: 0 $uni-spacing-base;
  height: $input-height;
  background: $uni-color-primary;
  color: white;
  border: none;
  font-size: $uni-font-size-sm;
  
  &.disabled {
    background: #ccc;
    color: #999;
  }
}

.error-text {
  font-size: $uni-font-size-sm;
  color: $uni-color-error;
  margin-top: $uni-spacing-xs;
}

.role-title {
  font-size: $uni-font-size-base;
  margin-bottom: $uni-spacing-base;
  color: $uni-text-color;
}

.role-options {
  display: flex;
  gap: $uni-spacing-base;
}

.role-option {
  flex: 1;
  padding: $uni-spacing-base;
  border: 1rpx solid $uni-border-color;
  border-radius: $uni-border-radius-base;
  text-align: center;
  background: #f8f9fa;
  transition: all 0.3s ease;
  
  &.active {
    border-color: $uni-color-primary;
    background: rgba($uni-color-primary, 0.1);
  }
}

.role-icon {
  display: block;
  font-size: $uni-font-size-xl;
  margin-bottom: $uni-spacing-xs;
}

.role-name {
  font-size: $uni-font-size-sm;
  color: $uni-text-color;
}

.login-btn {
  width: 100%;
  height: $button-height-lg;
  background: $uni-color-primary;
  color: white;
  border: none;
  border-radius: $uni-border-radius-base;
  font-size: $uni-font-size-lg;
  font-weight: 500;
  margin: $uni-spacing-xl 0 $uni-spacing-lg;
  
  &.loading {
    background: #ccc;
  }
}

.agreement {
  margin-bottom: $uni-spacing-base;
}

.agreement-item {
  display: flex;
  align-items: flex-start;
  gap: $uni-spacing-xs;
}

.agreement-text {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
  line-height: 1.5;
}

.link {
  color: $uni-color-primary;
}

.footer {
  text-align: center;
  padding: $uni-spacing-lg;
  color: rgba(255, 255, 255, 0.7);
}

.version, .company {
  display: block;
  font-size: $uni-font-size-sm;
  margin-bottom: $uni-spacing-xs;
}
</style>
