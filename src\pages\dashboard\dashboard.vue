<template>
  <view class="dashboard-container">
    <!-- 顶部导航栏 -->
    <view class="navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <!-- 公司信息区域 -->
        <view class="company-section">
          <view class="company-logo">
            <image class="logo-img" src="/static/logo.png" mode="aspectFit"></image>
          </view>
          <view class="company-info">
            <text class="company-name">{{ companyInfo.name }}</text>
            <text class="company-desc">{{ companyInfo.description }}</text>
          </view>
        </view>

        <!-- 用户信息区域 -->
        <view class="user-section">
          <view class="avatar-wrapper">
            <image class="avatar" :src="userInfo.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
            <view class="status-dot"></view>
          </view>
          <view class="user-info">
            <text class="greeting">{{ greeting }}，{{ userInfo.name || '用户' }}</text>
            <text class="subtitle">{{ getRoleText(userInfo.role) }}</text>
          </view>
        </view>

        <view class="nav-actions">
          <view class="nav-btn" @click="goToMessages">
            <uni-icons type="notification" size="20" color="#fff"></uni-icons>
            <view v-if="unreadCount > 0" class="badge">{{ unreadCount }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主内容区域 -->
    <scroll-view class="main-content" scroll-y enable-back-to-top>

      <!-- 数据概览卡片 -->
      <view class="overview-section">
        <view class="section-header">
          <text class="section-title">工作概览</text>
          <text class="section-subtitle">今日工作数据统计</text>
        </view>
        <view class="overview-grid">
          <view class="overview-card" v-for="item in overviewData" :key="item.key" @click="handleStatClick(item)">
            <view class="card-background" :style="{ background: `linear-gradient(135deg, ${item.color}15, ${item.color}05)` }"></view>
            <view class="card-content">
              <view class="card-header">
                <view class="card-icon" :style="{ backgroundColor: item.color }">
                  <uni-icons :type="item.icon" size="18" color="#fff"></uni-icons>
                </view>
                <view class="card-trend" :class="item.trend">
                  <uni-icons :type="item.trendIcon" size="12" :color="item.trendColor"></uni-icons>
                  <text class="trend-text">{{ item.trendText }}</text>
                </view>
              </view>
              <view class="card-body">
                <text class="card-number">{{ item.value }}</text>
                <text class="card-label">{{ item.label }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 快速操作卡片 -->
      <view class="actions-section">
        <view class="section-header">
          <text class="section-title">快速操作</text>
          <text class="section-subtitle">常用功能入口</text>
        </view>
        <view class="actions-card">
          <view class="actions-grid">
            <view
              class="action-item"
              v-for="action in quickActions"
              :key="action.key"
              @click="handleQuickAction(action)"
            >
              <view class="action-icon-wrapper">
                <view class="action-icon" :style="{ backgroundColor: action.color }">
                  <uni-icons :type="action.icon" size="20" color="#fff"></uni-icons>
                </view>
              </view>
              <text class="action-label">{{ action.label }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 最新任务卡片 -->
      <view class="tasks-section">
        <view class="section-header">
          <text class="section-title">最新任务</text>
          <view class="more-link" @click="goToTaskList">
            <text>查看全部</text>
            <uni-icons type="right" size="14" color="#4c6ef5"></uni-icons>
          </view>
        </view>
        <view class="tasks-card">
          <view v-if="recentTasks.length > 0">
            <view
              class="task-item"
              v-for="task in recentTasks"
              :key="task.id"
              @click="goToTaskDetail(task)"
            >
              <view class="task-left">
                <view class="task-status" :class="`status-${task.status}`">
                  <view class="status-dot"></view>
                </view>
                <view class="task-content">
                  <text class="task-title">{{ task.title }}</text>
                  <view class="task-meta">
                    <view class="task-address">
                      <uni-icons type="location" size="14" color="#ff6b6b"></uni-icons>
                      <text>{{ task.address }}</text>
                    </view>
                    <view class="task-time">
                      <uni-icons type="calendar" size="14" color="#94a3b8"></uni-icons>
                      <text>{{ task.time }}</text>
                    </view>
                  </view>
                </view>
              </view>
              <view class="task-right">
                <text class="task-type">{{ task.type }}</text>
                <view class="task-priority" :class="`priority-${task.priority}`">
                  {{ task.priorityText }}
                </view>
              </view>
            </view>
          </view>
          <view v-else class="empty-state">
            <uni-icons type="list" size="48" color="#cbd5e1"></uni-icons>
            <text class="empty-text">暂无任务</text>
          </view>
        </view>
      </view>

      <!-- 工作统计卡片 -->
      <view class="stats-section">
        <view class="section-header">
          <text class="section-title">工作统计</text>
          <text class="section-subtitle">本周工作数据</text>
        </view>
        <view class="stats-card">
          <view class="stats-row">
            <view class="stat-item">
              <view class="stat-icon">
                <uni-icons type="calendar" size="20" color="#4c6ef5"></uni-icons>
              </view>
              <view class="stat-info">
                <text class="stat-number">{{ todayTasks }}</text>
                <text class="stat-label">今日任务</text>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-icon">
                <uni-icons type="checkmarkempty" size="20" color="#52c41a"></uni-icons>
              </view>
              <view class="stat-info">
                <text class="stat-number">{{ completedTasks }}</text>
                <text class="stat-label">已完成</text>
              </view>
            </view>
          </view>
          <view class="stats-row">
            <view class="stat-item">
              <view class="stat-icon">
                <uni-icons type="clock" size="20" color="#faad14"></uni-icons>
              </view>
              <view class="stat-info">
                <text class="stat-number">{{ pendingTasks }}</text>
                <text class="stat-label">待处理</text>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-icon">
                <uni-icons type="fire" size="20" color="#ff4d4f"></uni-icons>
              </view>
              <view class="stat-info">
                <text class="stat-number">{{ urgentTasks }}</text>
                <text class="stat-label">紧急任务</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 悬浮操作按钮 -->
    <view class="fab" @click="showFabMenu">
      <uni-icons :type="fabMenuVisible ? 'closeempty' : 'plus'" size="24" color="#fff"></uni-icons>
    </view>

    <!-- 悬浮菜单 -->
    <view v-if="fabMenuVisible" class="fab-menu" @click="hideFabMenu">
      <view class="fab-menu-item" @click.stop="quickReport">
        <uni-icons type="compose" size="20" color="#4c6ef5"></uni-icons>
        <text class="fab-menu-text">快速上报</text>
      </view>
      <view class="fab-menu-item" @click.stop="openNavigation">
        <uni-icons type="navigate" size="20" color="#4c6ef5"></uni-icons>
        <text class="fab-menu-text">导航</text>
      </view>
      <view class="fab-menu-item" @click.stop="scanCode">
        <uni-icons type="scan" size="20" color="#4c6ef5"></uni-icons>
        <text class="fab-menu-text">扫码</text>
      </view>
    </view>
  </view>
</template>

<script>
import store from '@/stores/index.js'

export default {
  name: 'Dashboard',
  data() {
    return {
      statusBarHeight: 0,
      unreadCount: 3,
      fabMenuVisible: false,

      // 公司信息
      companyInfo: {
        name: '燃气管理系统',
        description: '专业的燃气设备维护管理平台',
        logo: '/static/logo.png',
        version: 'v1.0.0'
      },

      // 数据概览 - 使用uniapp图标
      overviewData: [
        {
          key: 'new',
          label: '新任务',
          value: 5,
          icon: 'list',
          color: '#1890ff',
          trend: 'up',
          trendIcon: 'up',
          trendText: '+12%',
          trendColor: '#52c41a'
        },
        {
          key: 'inProgress',
          label: '进行中',
          value: 3,
          icon: 'gear',
          color: '#faad14',
          trend: 'stable',
          trendIcon: 'minus',
          trendText: '0%',
          trendColor: 'rgba(0, 0, 0, 0.45)'
        },
        {
          key: 'completed',
          label: '已完成',
          value: 12,
          icon: 'checkmarkempty',
          color: '#52c41a',
          trend: 'up',
          trendIcon: 'up',
          trendText: '+8%',
          trendColor: '#52c41a'
        },
        {
          key: 'urgent',
          label: '紧急任务',
          value: 2,
          icon: 'fire',
          color: '#ff4d4f',
          trend: 'down',
          trendIcon: 'down',
          trendText: '-3%',
          trendColor: '#ff4d4f'
        }
      ],

      // 快速操作 - 使用uniapp图标
      quickActions: [
        { key: 'scan', label: '扫码', icon: 'scan', color: '#1890ff' },
        { key: 'report', label: '上报', icon: 'compose', color: '#52c41a' },
        { key: 'check', label: '巡检', icon: 'search', color: '#faad14' },
        { key: 'repair', label: '维修', icon: 'gear', color: '#ff4d4f' }
      ],
      
      // 最新任务
      recentTasks: [
        {
          id: 1,
          title: '燃气管道例行检查',
          address: '建设路123号',
          time: '09:30',
          type: '巡检',
          status: 'pending',
          priority: 'high',
          priorityText: '高'
        },
        {
          id: 2,
          title: '用户报修处理',
          address: '解放路456号',
          time: '14:00',
          type: '维修',
          status: 'inProgress',
          priority: 'medium',
          priorityText: '中'
        },
        {
          id: 3,
          title: '设备维护保养',
          address: '人民路789号',
          time: '16:30',
          type: '维保',
          status: 'completed',
          priority: 'low',
          priorityText: '低'
        }
      ],
      
      // 工作统计
      todayTasks: 12,
      completedTasks: 8,
      pendingTasks: 4,
      urgentTasks: 2
    }
  },
  
  computed: {
    userInfo() {
      return store.state.user
    },

    greeting() {
      const hour = new Date().getHours()
      if (hour < 12) return '早上好'
      if (hour < 18) return '下午好'
      return '晚上好'
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    this.refreshData()
  },
  
  methods: {
    // 初始化页面
    initPage() {
      // 获取状态栏高度
      const systemInfo = uni.getSystemInfoSync()
      this.statusBarHeight = systemInfo.statusBarHeight || 0
    },

    // 刷新数据
    refreshData() {
      // 这里可以调用API获取最新数据
      console.log('刷新数据')
    },

    // 获取角色文本
    getRoleText(role) {
      const roleMap = {
        'maintenance': '维保员',
        'inspector': '巡检员',
        'delivery': '配送员'
      }
      return roleMap[role] || '工作人员'
    },

    // 处理统计卡片点击
    handleStatClick(item) {
      console.log('点击统计卡片:', item.key)
      // 根据不同类型跳转到对应页面
      switch (item.key) {
        case 'new':
        case 'inProgress':
        case 'completed':
        case 'urgent':
          uni.switchTab({
            url: '/pages/task/task-list'
          })
          break
      }
    },

    // 处理快速操作
    handleQuickAction(action) {
      console.log('快速操作:', action.key)
      switch (action.key) {
        case 'scan':
          this.scanCode()
          break
        case 'report':
          this.quickReport()
          break
        case 'check':
          uni.navigateTo({
            url: '/pages/maintenance/maintenance'
          })
          break
        case 'repair':
          uni.navigateTo({
            url: '/pages/maintenance/maintenance'
          })
          break
      }
    },

    // 扫码功能
    scanCode() {
      uni.scanCode({
        success: (res) => {
          console.log('扫码结果:', res.result)
          uni.showToast({
            title: '扫码成功',
            icon: 'success'
          })
        },
        fail: (err) => {
          console.error('扫码失败:', err)
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          })
        }
      })
    },

    // 打开导航
    openNavigation() {
      uni.navigateTo({
        url: '/pages/navigation/navigation'
      })
    },

    // 快速上报
    quickReport() {
      uni.navigateTo({
        url: '/pages/report/report'
      })
    },

    // 跳转到任务列表
    goToTaskList() {
      uni.switchTab({
        url: '/pages/task/task-list'
      })
    },

    // 跳转到任务详情
    goToTaskDetail(task) {
      uni.navigateTo({
        url: `/pages/task/task-detail?id=${task.id}`
      })
    },

    // 跳转到消息中心
    goToMessages() {
      uni.navigateTo({
        url: '/pages/messages/messages'
      })
    },

    // 显示悬浮菜单
    showFabMenu() {
      this.fabMenuVisible = !this.fabMenuVisible
    },

    // 隐藏悬浮菜单
    hideFabMenu() {
      this.fabMenuVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
/* 现代化精美Dashboard设计 */
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
}

/* 精美顶部导航栏 */
.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.25);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }
}

.navbar-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 32rpx;
  position: relative;
  z-index: 1;
}

/* 公司信息区域 */
.company-section {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.company-logo {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.logo-img {
  width: 40rpx;
  height: 40rpx;
}

.company-info {
  flex: 1;
}

.company-name {
  font-size: 32rpx;
  font-weight: 700;
  color: white;
  line-height: 1.3;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.company-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4rpx;
}

/* 用户信息区域 */
.user-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}

.avatar-wrapper {
  position: relative;
}

.avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.status-dot {
  position: absolute;
  bottom: 2rpx;
  right: 2rpx;
  width: 16rpx;
  height: 16rpx;
  background: #10b981;
  border-radius: 50%;
  border: 2rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  flex: 1;
  margin-left: 16rpx;
}

.greeting {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  opacity: 0.95;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.nav-actions {
  display: flex;
  align-items: center;
}

.nav-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);

  &:active {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0.95);
  }
}

.nav-icon {
  font-size: 32rpx;
}

.badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1.2;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.4);
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 32rpx 24rpx;
}

/* 精美卡片样式 */
.overview-section,
.actions-section,
.tasks-section,
.stats-section {
  margin-bottom: 32rpx;
}

.section-header {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.3;
}

.section-subtitle {
  font-size: 26rpx;
  color: #64748b;
  font-weight: 400;
}

/* 精美数据概览卡片 */
.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.overview-card {
  position: relative;
  background: white;
  border-radius: 24rpx;
  padding: 0;
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;

  &:hover {
    transform: translateY(-8rpx);
    box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.12);
    border-color: rgba(102, 126, 234, 0.3);
  }

  &:active {
    transform: translateY(-4rpx);
  }
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.6;
}

.card-content {
  position: relative;
  z-index: 1;
  padding: 32rpx;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.card-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
    border-radius: 20rpx;
  }
}

.icon-text {
  font-size: 32rpx;
  position: relative;
  z-index: 1;
}

.card-trend {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;

  &.up {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
  }

  &.down {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
  }

  &.stable {
    background: rgba(100, 116, 139, 0.1);
    color: #475569;
  }
}

.trend-text {
  font-weight: 600;
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.card-number {
  font-size: 56rpx;
  font-weight: 800;
  color: #0f172a;
  line-height: 1.1;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

.card-label {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
}

/* 精美快速操作卡片 */
.actions-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 16rpx;
  border-radius: 20rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;

  &:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    transform: translateY(-4rpx);
  }

  &:active {
    transform: translateY(-2rpx) scale(0.98);
  }
}

.action-icon-wrapper {
  position: relative;
}

.action-icon {
  width: 72rpx;
  height: 72rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
    border-radius: 20rpx;
  }
}

.icon-text {
  font-size: 32rpx;
  position: relative;
  z-index: 1;
}

.action-label {
  font-size: 26rpx;
  color: #334155;
  font-weight: 600;
  text-align: center;
}

/* 精美任务列表卡片 */
.tasks-section .section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.more-link {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #667eea;
  font-weight: 600;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  background: rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;

  &:active {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(0.95);
  }
}

.arrow {
  font-size: 20rpx;
  font-weight: 700;
}

.tasks-card {
  background: white;
  border-radius: 24rpx;
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.task-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(241, 245, 249, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.03), rgba(118, 75, 162, 0.03));
    transform: translateX(8rpx);
  }

  &:active {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
  }

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6rpx;
    background: transparent;
    transition: all 0.3s ease;
  }

  &:hover::before {
    background: linear-gradient(135deg, #667eea, #764ba2);
  }
}

.task-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.task-status {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .status-dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  }

  &.status-pending {
    background: rgba(251, 191, 36, 0.2);

    .status-dot {
      background: linear-gradient(135deg, #fbbf24, #f59e0b);
    }
  }

  &.status-inProgress {
    background: rgba(59, 130, 246, 0.2);

    .status-dot {
      background: linear-gradient(135deg, #3b82f6, #2563eb);
    }
  }

  &.status-completed {
    background: rgba(34, 197, 94, 0.2);

    .status-dot {
      background: linear-gradient(135deg, #22c55e, #16a34a);
    }
  }
}

.task-content {
  flex: 1;
}

.task-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.task-address {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #64748b;
  padding: 6rpx 12rpx;
  background: rgba(100, 116, 139, 0.1);
  border-radius: 12rpx;
}

.task-time {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #94a3b8;
  font-weight: 500;
}

.task-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;
}

.task-type {
  font-size: 24rpx;
  color: #475569;
  font-weight: 500;
  padding: 4rpx 12rpx;
  background: rgba(71, 85, 105, 0.1);
  border-radius: 12rpx;
}

.task-priority {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 600;
  line-height: 1.2;

  &.priority-high {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(220, 38, 38, 0.15));
    color: #dc2626;
    border: 1rpx solid rgba(239, 68, 68, 0.3);
    box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.2);
  }

  &.priority-medium {
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.15), rgba(245, 158, 11, 0.15));
    color: #f59e0b;
    border: 1rpx solid rgba(251, 191, 36, 0.3);
    box-shadow: 0 2rpx 8rpx rgba(251, 191, 36, 0.2);
  }

  &.priority-low {
    background: linear-gradient(135deg, rgba(100, 116, 139, 0.15), rgba(71, 85, 105, 0.15));
    color: #475569;
    border: 1rpx solid rgba(100, 116, 139, 0.3);
    box-shadow: 0 2rpx 8rpx rgba(100, 116, 139, 0.2);
  }
}

/* 精美工作统计卡片 */
.stats-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.stats-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.stat-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.8));
  border: 1rpx solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  &:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    transform: translateY(-4rpx);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  }

  &:active {
    transform: translateY(-2rpx);
  }
}

.stat-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 16rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #0f172a;
  line-height: 1.2;
}

.stat-label {
  font-size: 24rpx;
  color: #64748b;
  margin-top: 4rpx;
  font-weight: 500;
}

/* 精美空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  padding: 80rpx 32rpx;
}

.empty-icon {
  font-size: 96rpx;
  color: #cbd5e1;
  opacity: 0.8;
}

.empty-text {
  font-size: 32rpx;
  color: #94a3b8;
  font-weight: 500;
}

/* 超精美悬浮按钮 */
.fab {
  position: fixed;
  bottom: 140rpx;
  right: 32rpx;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 0 0 0 rgba(102, 126, 234, 0.7),
    0 16rpx 40rpx rgba(102, 126, 234, 0.4),
    0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  animation: fabPulse 3s infinite;

  &::before {
    content: '';
    position: absolute;
    top: -4rpx;
    left: -4rpx;
    right: -4rpx;
    bottom: -4rpx;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
    border-radius: 50%;
    z-index: -1;
  }

  &::after {
    content: '';
    position: absolute;
    top: 20rpx;
    left: 20rpx;
    right: 20rpx;
    bottom: 20rpx;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.1));
    border-radius: 50%;
    backdrop-filter: blur(10rpx);
  }

  &:active {
    transform: scale(0.85);
    box-shadow:
      0 0 0 0 rgba(102, 126, 234, 0.7),
      0 8rpx 24rpx rgba(102, 126, 234, 0.3),
      0 4rpx 8rpx rgba(0, 0, 0, 0.1);
    animation: none;
  }
}

/* 悬浮按钮图标样式已通过uni-icons组件处理 */

/* 精美悬浮菜单 */
.fab-menu {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(8rpx);
  z-index: 99;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
  padding: 32rpx;
  padding-bottom: 280rpx;
}

.fab-menu-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background: white;
  padding: 20rpx 32rpx;
  border-radius: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 0.3s ease;

  &:active {
    background: rgba(248, 250, 252, 0.9);
    transform: scale(0.95);
  }
}

.fab-menu-text {
  font-size: 30rpx;
  color: #1e293b;
  font-weight: 600;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 140rpx;
}

/* 精美动画 */
@keyframes slideInRight {
  from {
    transform: translateX(120rpx);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fabPulse {
  0% {
    box-shadow:
      0 0 0 0 rgba(102, 126, 234, 0.7),
      0 16rpx 40rpx rgba(102, 126, 234, 0.4),
      0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow:
      0 0 0 20rpx rgba(102, 126, 234, 0),
      0 20rpx 50rpx rgba(102, 126, 234, 0.5),
      0 12rpx 20rpx rgba(0, 0, 0, 0.15);
  }
  100% {
    box-shadow:
      0 0 0 0 rgba(102, 126, 234, 0),
      0 16rpx 40rpx rgba(102, 126, 234, 0.4),
      0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  }
}
</style>