<template>
  <view class="profile-container">
    <!-- 用户信息头部 -->
    <view class="profile-header">
      <view class="user-info">
        <image 
          class="avatar" 
          :src="userInfo.avatar || '/static/default-avatar.png'" 
          mode="aspectFill"
          @click="changeAvatar"
        />
        <view class="user-details">
          <text class="username">{{ userInfo.name || '未设置姓名' }}</text>
          <text class="work-number">工号：{{ userInfo.workNumber || '未设置' }}</text>
          <text class="role-text">{{ getRoleText(userInfo.role) }}</text>
        </view>
        <view class="edit-btn" @click="editProfile">
          <text class="edit-icon">✏️</text>
        </view>
      </view>
      
      <!-- 签到状态 -->
      <view class="checkin-status">
        <view class="status-card" :class="{ active: isCheckedIn }">
          <text class="status-text">{{ isCheckedIn ? '已签到' : '未签到' }}</text>
          <text class="status-time">{{ checkinTime || '-- : --' }}</text>
        </view>
        <button 
          class="checkin-btn"
          :class="{ checked: isCheckedIn }"
          @click="toggleCheckin"
        >
          {{ isCheckedIn ? '签退' : '签到' }}
        </button>
      </view>
    </view>
    
    <!-- 数据统计 -->
    <view class="stats-section">
      <view class="section-title">本月数据</view>
      <view class="stats-grid">
        <view class="stat-item" v-for="stat in statsData" :key="stat.key">
          <text class="stat-number">{{ stat.value }}</text>
          <text class="stat-label">{{ stat.label }}</text>
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group" v-for="group in menuGroups" :key="group.title">
        <view class="group-title">{{ group.title }}</view>
        <view class="menu-list">
          <view 
            class="menu-item"
            v-for="item in group.items"
            :key="item.key"
            @click="handleMenuClick(item)"
          >
            <view class="menu-icon">
              <text class="icon-text">{{ item.icon }}</text>
            </view>
            <text class="menu-label">{{ item.label }}</text>
            <view class="menu-extra">
              <text v-if="item.badge" class="badge">{{ item.badge }}</text>
              <text class="arrow">›</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @click="handleLogout">
        退出登录
      </button>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script>
import store from '@/stores/index.js'
import { userApi } from '@/api/index.js'
import { formatDate, chooseImage, getLocation, showToast, showConfirm } from '@/utils/index.js'

export default {
  data() {
    return {
      isCheckedIn: false,
      checkinTime: '',
      statsData: [
        { key: 'completed', label: '已完成', value: 28 },
        { key: 'inProgress', label: '进行中', value: 3 },
        { key: 'workHours', label: '工作时长', value: '186h' },
        { key: 'rating', label: '满意度', value: '98%' }
      ],
      menuGroups: [
        {
          title: '工作管理',
          items: [
            { key: 'myTasks', label: '我的工单', icon: '📋' },
            { key: 'schedule', label: '工作日程', icon: '📅' },
            { key: 'workReport', label: '工作报告', icon: '📊' },
            { key: 'attendance', label: '考勤记录', icon: '⏰' }
          ]
        },
        {
          title: '消息通知',
          items: [
            { key: 'messages', label: '消息中心', icon: '💬', badge: this.unreadCount > 0 ? this.unreadCount : null },
            { key: 'notifications', label: '系统通知', icon: '🔔' },
            { key: 'announcements', label: '公告通知', icon: '📢' }
          ]
        },
        {
          title: '工具与支持',
          items: [
            { key: 'knowledge', label: '知识库', icon: '📚' },
            { key: 'manuals', label: '技术手册', icon: '📖' },
            { key: 'feedback', label: '意见反馈', icon: '💭' },
            { key: 'help', label: '帮助中心', icon: '❓' }
          ]
        },
        {
          title: '设置',
          items: [
            { key: 'settings', label: '应用设置', icon: '⚙️' },
            { key: 'privacy', label: '隐私设置', icon: '🔒' },
            { key: 'about', label: '关于我们', icon: 'ℹ️' }
          ]
        }
      ]
    }
  },
  
  computed: {
    userInfo() {
      return store.state.user
    },
    
    unreadCount() {
      return store.state.messages.unreadCount
    }
  },
  
  onLoad() {
    this.loadUserData()
    this.checkCheckinStatus()
  },
  
  onShow() {
    this.loadUserData()
  },
  
  methods: {
    // 加载用户数据
    async loadUserData() {
      try {
        // 这里可以从API获取最新的用户数据和统计信息
        // const userData = await userApi.getUserInfo()
        // 更新统计数据等
      } catch (error) {
        console.error('加载用户数据失败:', error)
      }
    },
    
    // 检查签到状态
    checkCheckinStatus() {
      const today = formatDate(new Date(), 'YYYY-MM-DD')
      const checkinData = uni.getStorageSync(`checkin_${today}`)
      if (checkinData) {
        this.isCheckedIn = checkinData.isCheckedIn
        this.checkinTime = checkinData.checkinTime
      }
    },
    
    // 获取角色文本
    getRoleText(role) {
      const roleMap = {
        maintenance: '维保员',
        inspector: '巡检员',
        delivery: '配送员'
      }
      return roleMap[role] || '未设置角色'
    },
    
    // 更换头像
    async changeAvatar() {
      try {
        const images = await chooseImage(1)
        if (images && images.length > 0) {
          // 上传头像
          showToast('头像上传功能开发中...')
          // const avatarUrl = await userApi.uploadAvatar(images[0])
          // store.setUser({ avatar: avatarUrl })
        }
      } catch (error) {
        showToast('选择图片失败')
      }
    },
    
    // 编辑个人资料
    editProfile() {
      uni.navigateTo({
        url: '/pages/profile/edit-profile'
      })
    },
    
    // 切换签到状态
    async toggleCheckin() {
      try {
        const location = await getLocation()
        const now = new Date()
        const today = formatDate(now, 'YYYY-MM-DD')
        const time = formatDate(now, 'HH:mm')
        
        if (!this.isCheckedIn) {
          // 签到
          this.isCheckedIn = true
          this.checkinTime = time
          
          // 保存签到信息
          uni.setStorageSync(`checkin_${today}`, {
            isCheckedIn: true,
            checkinTime: time,
            checkinLocation: location
          })
          
          showToast('签到成功')
        } else {
          // 签退
          const confirmed = await showConfirm('确认签退？')
          if (confirmed) {
            this.isCheckedIn = false
            this.checkinTime = ''
            
            // 更新签到信息
            const checkinData = uni.getStorageSync(`checkin_${today}`)
            if (checkinData) {
              checkinData.isCheckedIn = false
              checkinData.checkoutTime = time
              checkinData.checkoutLocation = location
              uni.setStorageSync(`checkin_${today}`, checkinData)
            }
            
            showToast('签退成功')
          }
        }
      } catch (error) {
        showToast('操作失败，请检查定位权限')
      }
    },
    
    // 处理菜单点击
    handleMenuClick(item) {
      switch (item.key) {
        case 'myTasks':
          uni.switchTab({
            url: '/pages/task/task-list'
          })
          break
        case 'schedule':
          uni.navigateTo({
            url: '/pages/schedule/schedule'
          })
          break
        case 'workReport':
          uni.navigateTo({
            url: '/pages/report/work-report'
          })
          break
        case 'attendance':
          uni.navigateTo({
            url: '/pages/attendance/attendance'
          })
          break
        case 'messages':
          uni.navigateTo({
            url: '/pages/messages/messages'
          })
          break
        case 'notifications':
          uni.navigateTo({
            url: '/pages/notifications/notifications'
          })
          break
        case 'announcements':
          uni.navigateTo({
            url: '/pages/announcements/announcements'
          })
          break
        case 'knowledge':
          uni.switchTab({
            url: '/pages/knowledge/knowledge'
          })
          break
        case 'manuals':
          uni.navigateTo({
            url: '/pages/knowledge/manuals'
          })
          break
        case 'feedback':
          uni.navigateTo({
            url: '/pages/feedback/feedback'
          })
          break
        case 'help':
          uni.navigateTo({
            url: '/pages/help/help'
          })
          break
        case 'settings':
          uni.navigateTo({
            url: '/pages/settings/settings'
          })
          break
        case 'privacy':
          uni.navigateTo({
            url: '/pages/settings/privacy'
          })
          break
        case 'about':
          uni.navigateTo({
            url: '/pages/about/about'
          })
          break
        default:
          showToast('功能开发中...')
      }
    },
    
    // 退出登录
    async handleLogout() {
      const confirmed = await showConfirm('确认退出登录？')
      if (confirmed) {
        store.logout()
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.profile-header {
  background: linear-gradient(135deg, $uni-color-primary 0%, $uni-color-primary-dark 100%);
  color: white;
  padding: $uni-spacing-lg;
  padding-top: calc($uni-spacing-lg + 44px); // 状态栏高度
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: $uni-spacing-xl;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: $uni-spacing-lg;
}

.user-details {
  flex: 1;
}

.username {
  display: block;
  font-size: $uni-font-size-xl;
  font-weight: bold;
  margin-bottom: $uni-spacing-xs;
}

.work-number {
  display: block;
  font-size: $uni-font-size-base;
  opacity: 0.8;
  margin-bottom: $uni-spacing-xs;
}

.role-text {
  display: block;
  font-size: $uni-font-size-sm;
  opacity: 0.7;
}

.edit-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.edit-icon {
  font-size: $uni-font-size-lg;
}

.checkin-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border-radius: $uni-border-radius-lg;
  padding: $uni-spacing-base $uni-spacing-lg;
  margin-right: $uni-spacing-base;
  
  &.active {
    background: rgba(76, 175, 80, 0.2);
  }
}

.status-text {
  display: block;
  font-size: $uni-font-size-base;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.status-time {
  display: block;
  font-size: $uni-font-size-sm;
  opacity: 0.8;
}

.checkin-btn {
  padding: $uni-spacing-base $uni-spacing-xl;
  background: white;
  color: $uni-color-primary;
  border: none;
  border-radius: $uni-border-radius-lg;
  font-size: $uni-font-size-base;
  font-weight: 500;
  
  &.checked {
    background: rgba(255, 255, 255, 0.2);
    color: white;
  }
}

.stats-section {
  background: white;
  margin: $uni-spacing-lg;
  border-radius: $uni-border-radius-lg;
  padding: $uni-spacing-lg;
  box-shadow: $shadow-sm;
}

.section-title {
  font-size: $uni-font-size-lg;
  font-weight: 500;
  color: $uni-text-color;
  margin-bottom: $uni-spacing-lg;
}

.stats-grid {
  display: flex;
  gap: $uni-spacing-lg;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  display: block;
  font-size: $uni-font-size-2xl;
  font-weight: bold;
  color: $uni-color-primary;
  margin-bottom: $uni-spacing-xs;
}

.stat-label {
  display: block;
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
}

.menu-section {
  padding: 0 $uni-spacing-lg;
}

.menu-group {
  margin-bottom: $uni-spacing-lg;
}

.group-title {
  font-size: $uni-font-size-base;
  color: $uni-text-color-secondary;
  margin-bottom: $uni-spacing-base;
  padding-left: $uni-spacing-base;
}

.menu-list {
  background: white;
  border-radius: $uni-border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-sm;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: $uni-spacing-lg;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: #f8f9fa;
  }
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
  margin-right: $uni-spacing-base;
}

.icon-text {
  font-size: $uni-font-size-lg;
}

.menu-label {
  flex: 1;
  font-size: $uni-font-size-base;
  color: $uni-text-color;
}

.menu-extra {
  display: flex;
  align-items: center;
  gap: $uni-spacing-sm;
}

.badge {
  background: #F44336;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
}

.arrow {
  font-size: $uni-font-size-xl;
  color: #ccc;
}

.logout-section {
  padding: $uni-spacing-lg;
}

.logout-btn {
  width: 100%;
  height: $button-height-lg;
  background: #F44336;
  color: white;
  border: none;
  border-radius: $uni-border-radius-lg;
  font-size: $uni-font-size-lg;
  font-weight: 500;
}
</style>
