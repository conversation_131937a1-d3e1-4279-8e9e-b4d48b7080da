<template>
  <view class="maintenance-container">
    <!-- 顶部工具栏 -->
    <view class="toolbar">
      <view class="location-info">
        <text class="location-icon">📍</text>
        <text class="location-text">{{ currentLocation || '获取位置中...' }}</text>
      </view>
      <view class="toolbar-actions">
        <view class="action-btn" @click="refreshLocation">
          <text class="action-icon">🔄</text>
        </view>
        <view class="action-btn" @click="openMap">
          <text class="action-icon">🗺️</text>
        </view>
      </view>
    </view>
    
    <!-- 快速操作区域 -->
    <view class="quick-actions">
      <view class="action-grid">
        <view 
          class="action-item"
          v-for="action in quickActions"
          :key="action.key"
          @click="handleQuickAction(action)"
        >
          <view class="action-icon-bg" :style="{ backgroundColor: action.color }">
            <text class="action-icon">{{ action.icon }}</text>
          </view>
          <text class="action-label">{{ action.label }}</text>
        </view>
      </view>
    </view>
    
    <!-- 当前任务卡片 -->
    <view v-if="currentTask" class="current-task">
      <view class="task-header">
        <text class="task-title">当前任务</text>
        <view class="task-status" :class="'status-' + currentTask.status">
          <text class="status-text">{{ getStatusText(currentTask.status) }}</text>
        </view>
      </view>
      <view class="task-content">
        <text class="task-number">{{ currentTask.taskNumber }}</text>
        <text class="task-desc">{{ currentTask.title }}</text>
        <text class="task-address">📍 {{ currentTask.address }}</text>
        <view class="task-actions">
          <button class="task-btn primary" @click="goToTaskDetail">查看详情</button>
          <button class="task-btn secondary" @click="startWork">开始作业</button>
        </view>
      </view>
    </view>
    
    <!-- 工作流程指导 -->
    <view class="workflow-section">
      <view class="section-header">
        <text class="section-title">🔧 工作流程</text>
        <text class="expand-btn" @click="toggleWorkflow">{{ workflowExpanded ? '收起' : '展开' }}</text>
      </view>
      
      <view v-if="workflowExpanded" class="workflow-content">
        <view 
          class="workflow-step"
          :class="{ active: step.status === 'active', completed: step.status === 'completed' }"
          v-for="(step, index) in workflowSteps"
          :key="index"
          @click="handleStepClick(step, index)"
        >
          <view class="step-indicator">
            <text class="step-number">{{ index + 1 }}</text>
          </view>
          <view class="step-content">
            <text class="step-title">{{ step.title }}</text>
            <text class="step-desc">{{ step.description }}</text>
            <view v-if="step.tools && step.tools.length > 0" class="step-tools">
              <text 
                class="tool-tag"
                v-for="tool in step.tools"
                :key="tool"
              >
                {{ tool }}
              </text>
            </view>
          </view>
          <view class="step-action">
            <text v-if="step.status === 'completed'" class="check-icon">✓</text>
            <text v-else-if="step.status === 'active'" class="arrow-icon">▶</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 工具箱 -->
    <view class="toolbox-section">
      <view class="section-title">🧰 工具箱</view>
      <view class="tool-grid">
        <view 
          class="tool-item"
          v-for="tool in toolboxItems"
          :key="tool.key"
          @click="handleToolClick(tool)"
        >
          <view class="tool-icon">
            <text class="icon-text">{{ tool.icon }}</text>
          </view>
          <text class="tool-label">{{ tool.label }}</text>
        </view>
      </view>
    </view>
    
    <!-- 最近记录 -->
    <view class="recent-records">
      <view class="section-header">
        <text class="section-title">📝 最近记录</text>
        <text class="more-btn" @click="viewAllRecords">查看全部</text>
      </view>
      
      <view v-if="recentRecords.length > 0" class="record-list">
        <view 
          class="record-item"
          v-for="record in recentRecords"
          :key="record.id"
          @click="viewRecord(record)"
        >
          <view class="record-icon">
            <text class="icon-text">{{ getRecordIcon(record.type) }}</text>
          </view>
          <view class="record-content">
            <text class="record-title">{{ record.title }}</text>
            <text class="record-time">{{ formatTime(record.time) }}</text>
          </view>
          <view class="record-status">
            <text class="status-dot" :class="'status-' + record.status"></text>
          </view>
        </view>
      </view>
      
      <view v-else class="empty-records">
        <text class="empty-icon">📋</text>
        <text class="empty-text">暂无工作记录</text>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script>
import { taskApi } from '@/api/index.js'
import { getLocation, scanCode, chooseImage, formatRelativeTime, showToast, openMap as openMapUtil } from '@/utils/index.js'

export default {
  data() {
    return {
      currentLocation: '',
      workflowExpanded: true,
      currentTask: null,
      quickActions: [
        { key: 'scan', label: '扫码', icon: '📷', color: '#2196F3' },
        { key: 'photo', label: '拍照', icon: '📸', color: '#4CAF50' },
        { key: 'record', label: '记录', icon: '📝', color: '#FF9800' },
        { key: 'measure', label: '测量', icon: '📏', color: '#9C27B0' }
      ],
      workflowSteps: [
        {
          title: '到达现场',
          description: '确认到达客户指定地址',
          status: 'completed',
          tools: ['GPS定位', '拍照确认']
        },
        {
          title: '设备检查',
          description: '检查设备外观和基本状态',
          status: 'active',
          tools: ['扫码识别', '外观检查']
        },
        {
          title: '参数测量',
          description: '测量设备运行参数',
          status: 'pending',
          tools: ['测量工具', '数据记录']
        },
        {
          title: '维护操作',
          description: '执行具体的维护工作',
          status: 'pending',
          tools: ['维修工具', '更换配件']
        },
        {
          title: '测试验收',
          description: '测试设备功能并验收',
          status: 'pending',
          tools: ['功能测试', '性能验证']
        },
        {
          title: '完成签收',
          description: '客户确认并签字',
          status: 'pending',
          tools: ['电子签名', '满意度评价']
        }
      ],
      toolboxItems: [
        { key: 'calculator', label: '计算器', icon: '🧮' },
        { key: 'timer', label: '计时器', icon: '⏱️' },
        { key: 'flashlight', label: '手电筒', icon: '🔦' },
        { key: 'level', label: '水平仪', icon: '📐' },
        { key: 'compass', label: '指南针', icon: '🧭' },
        { key: 'ruler', label: '尺子', icon: '📏' }
      ],
      recentRecords: []
    }
  },
  
  onLoad() {
    this.getCurrentLocation()
    this.loadCurrentTask()
    this.loadRecentRecords()
  },
  
  onShow() {
    this.loadCurrentTask()
    this.loadRecentRecords()
  },
  
  methods: {
    // 获取当前位置
    async getCurrentLocation() {
      try {
        const location = await getLocation()
        this.currentLocation = location.address || `${location.latitude}, ${location.longitude}`
      } catch (error) {
        this.currentLocation = '位置获取失败'
      }
    },
    
    // 刷新位置
    refreshLocation() {
      this.currentLocation = '获取位置中...'
      this.getCurrentLocation()
    },
    
    // 打开地图
    openMap() {
      openMapUtil()
    },
    
    // 加载当前任务
    async loadCurrentTask() {
      try {
        const tasks = await taskApi.getTaskList({ status: 'progress', limit: 1 })
        this.currentTask = tasks.length > 0 ? tasks[0] : null
      } catch (error) {
        console.error('加载当前任务失败:', error)
      }
    },
    
    // 加载最近记录
    async loadRecentRecords() {
      try {
        // 模拟数据
        this.recentRecords = [
          {
            id: 1,
            type: 'maintenance',
            title: '燃气表维护记录',
            time: new Date(Date.now() - 2 * 60 * 60 * 1000),
            status: 'completed'
          },
          {
            id: 2,
            type: 'inspection',
            title: '安全检查记录',
            time: new Date(Date.now() - 4 * 60 * 60 * 1000),
            status: 'completed'
          }
        ]
      } catch (error) {
        console.error('加载记录失败:', error)
      }
    },
    
    // 处理快速操作
    async handleQuickAction(action) {
      switch (action.key) {
        case 'scan':
          await this.scanCode()
          break
        case 'photo':
          await this.takePhoto()
          break
        case 'record':
          this.addRecord()
          break
        case 'measure':
          this.openMeasureTool()
          break
      }
    },
    
    // 扫码功能
    async scanCode() {
      try {
        const result = await scanCode()
        showToast('扫码结果: ' + result)
        // 处理扫码结果
      } catch (error) {
        showToast('扫码失败')
      }
    },
    
    // 拍照功能
    async takePhoto() {
      try {
        const images = await chooseImage(1)
        if (images && images.length > 0) {
          showToast('照片已保存')
          // 处理照片
        }
      } catch (error) {
        showToast('拍照失败')
      }
    },
    
    // 添加记录
    addRecord() {
      uni.navigateTo({
        url: '/pages/maintenance/add-record'
      })
    },
    
    // 打开测量工具
    openMeasureTool() {
      uni.navigateTo({
        url: '/pages/tools/measure'
      })
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        new: '新任务',
        accepted: '已接收',
        progress: '进行中',
        completed: '已完成'
      }
      return statusMap[status] || status
    },
    
    // 跳转到任务详情
    goToTaskDetail() {
      if (this.currentTask) {
        uni.navigateTo({
          url: `/pages/task/task-detail?id=${this.currentTask.id}`
        })
      }
    },
    
    // 开始作业
    startWork() {
      if (this.currentTask) {
        uni.navigateTo({
          url: `/pages/maintenance/work-process?taskId=${this.currentTask.id}`
        })
      }
    },
    
    // 切换工作流程展开状态
    toggleWorkflow() {
      this.workflowExpanded = !this.workflowExpanded
    },
    
    // 处理步骤点击
    handleStepClick(step, index) {
      if (step.status === 'active') {
        // 跳转到对应的操作页面
        uni.navigateTo({
          url: `/pages/maintenance/step-${index + 1}`
        })
      }
    },
    
    // 处理工具点击
    handleToolClick(tool) {
      switch (tool.key) {
        case 'calculator':
          uni.navigateTo({
            url: '/pages/tools/calculator'
          })
          break
        case 'timer':
          uni.navigateTo({
            url: '/pages/tools/timer'
          })
          break
        case 'flashlight':
          this.toggleFlashlight()
          break
        case 'level':
          uni.navigateTo({
            url: '/pages/tools/level'
          })
          break
        case 'compass':
          uni.navigateTo({
            url: '/pages/tools/compass'
          })
          break
        case 'ruler':
          uni.navigateTo({
            url: '/pages/tools/ruler'
          })
          break
        default:
          showToast('工具开发中...')
      }
    },
    
    // 切换手电筒
    toggleFlashlight() {
      // #ifdef APP-PLUS
      const flashlight = plus.camera.getCamera()
      flashlight.setFlashMode('torch')
      showToast('手电筒已打开')
      // #endif
      
      // #ifndef APP-PLUS
      showToast('手电筒功能仅在APP中可用')
      // #endif
    },
    
    // 获取记录图标
    getRecordIcon(type) {
      const iconMap = {
        maintenance: '🔧',
        inspection: '🔍',
        repair: '⚡',
        install: '🔨'
      }
      return iconMap[type] || '📝'
    },
    
    // 格式化时间
    formatTime(time) {
      return formatRelativeTime(time)
    },
    
    // 查看记录
    viewRecord(record) {
      uni.navigateTo({
        url: `/pages/maintenance/record-detail?id=${record.id}`
      })
    },
    
    // 查看所有记录
    viewAllRecords() {
      uni.navigateTo({
        url: '/pages/maintenance/records'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.maintenance-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.toolbar {
  background: white;
  padding: $uni-spacing-base $uni-spacing-lg;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.location-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.location-icon {
  font-size: $uni-font-size-lg;
  margin-right: $uni-spacing-sm;
  color: $uni-color-primary;
}

.location-text {
  font-size: $uni-font-size-base;
  color: $uni-text-color;
  flex: 1;
}

.toolbar-actions {
  display: flex;
  gap: $uni-spacing-sm;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.action-icon {
  font-size: $uni-font-size-lg;
}

.quick-actions {
  background: white;
  margin: $uni-spacing-base $uni-spacing-lg;
  border-radius: $uni-border-radius-lg;
  padding: $uni-spacing-lg;
  box-shadow: $shadow-sm;
}

.action-grid {
  display: flex;
  gap: $uni-spacing-lg;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $uni-spacing-sm;
}

.action-icon-bg {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-label {
  font-size: $uni-font-size-sm;
  color: $uni-text-color;
}

.current-task {
  background: white;
  margin: 0 $uni-spacing-lg $uni-spacing-base;
  border-radius: $uni-border-radius-lg;
  box-shadow: $shadow-sm;
  overflow: hidden;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $uni-spacing-lg;
  border-bottom: 1rpx solid #f0f0f0;
}

.task-title {
  font-size: $uni-font-size-lg;
  font-weight: 500;
  color: $uni-text-color;
}

.task-status {
  padding: 8rpx 16rpx;
  border-radius: $uni-border-radius-lg;
  
  &.status-progress {
    background: rgba($status-progress, 0.1);
  }
}

.status-text {
  font-size: $uni-font-size-sm;
  color: $status-progress;
}

.task-content {
  padding: $uni-spacing-lg;
}

.task-number {
  font-size: $uni-font-size-base;
  color: $uni-color-primary;
  font-weight: 500;
  margin-bottom: $uni-spacing-xs;
}

.task-desc {
  font-size: $uni-font-size-lg;
  color: $uni-text-color;
  margin-bottom: $uni-spacing-xs;
}

.task-address {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
  margin-bottom: $uni-spacing-lg;
}

.task-actions {
  display: flex;
  gap: $uni-spacing-base;
}

.task-btn {
  flex: 1;
  height: 80rpx;
  border-radius: $uni-border-radius-base;
  font-size: $uni-font-size-base;
  border: none;
  
  &.primary {
    background: $uni-color-primary;
    color: white;
  }
  
  &.secondary {
    background: #f8f9fa;
    color: $uni-text-color;
  }
}

.workflow-section, .toolbox-section, .recent-records {
  background: white;
  margin: 0 $uni-spacing-lg $uni-spacing-base;
  border-radius: $uni-border-radius-lg;
  box-shadow: $shadow-sm;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $uni-spacing-lg;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: $uni-font-size-lg;
  font-weight: 500;
  color: $uni-text-color;
}

.expand-btn, .more-btn {
  font-size: $uni-font-size-sm;
  color: $uni-color-primary;
}

.workflow-content {
  padding: $uni-spacing-lg;
}

.workflow-step {
  display: flex;
  align-items: center;
  padding: $uni-spacing-base 0;
  
  &:not(:last-child) {
    border-bottom: 1rpx solid #f0f0f0;
  }
  
  &.completed {
    .step-indicator {
      background: $uni-color-success;
      color: white;
    }
  }
  
  &.active {
    .step-indicator {
      background: $uni-color-primary;
      color: white;
    }
  }
}

.step-indicator {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $uni-spacing-base;
  flex-shrink: 0;
}

.step-number {
  font-size: $uni-font-size-base;
  font-weight: 500;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: $uni-font-size-base;
  font-weight: 500;
  color: $uni-text-color;
  margin-bottom: 4rpx;
}

.step-desc {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
  margin-bottom: $uni-spacing-xs;
}

.step-tools {
  display: flex;
  gap: $uni-spacing-xs;
  flex-wrap: wrap;
}

.tool-tag {
  font-size: 20rpx;
  color: $uni-color-primary;
  background: rgba($uni-color-primary, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.step-action {
  width: 40rpx;
  text-align: center;
}

.check-icon {
  color: $uni-color-success;
  font-size: $uni-font-size-lg;
  font-weight: bold;
}

.arrow-icon {
  color: $uni-color-primary;
  font-size: $uni-font-size-base;
}

.tool-grid {
  display: flex;
  flex-wrap: wrap;
  padding: $uni-spacing-lg;
  gap: $uni-spacing-lg;
}

.tool-item {
  width: calc(33.333% - 16rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $uni-spacing-sm;
}

.tool-icon {
  width: 100rpx;
  height: 100rpx;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-text {
  font-size: $uni-font-size-xl;
}

.tool-label {
  font-size: $uni-font-size-sm;
  color: $uni-text-color;
  text-align: center;
}

.record-list {
  padding: $uni-spacing-lg;
}

.record-item {
  display: flex;
  align-items: center;
  padding: $uni-spacing-base 0;
  
  &:not(:last-child) {
    border-bottom: 1rpx solid #f0f0f0;
  }
}

.record-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $uni-spacing-base;
}

.record-content {
  flex: 1;
}

.record-title {
  font-size: $uni-font-size-base;
  color: $uni-text-color;
  margin-bottom: 4rpx;
}

.record-time {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
}

.record-status {
  width: 40rpx;
  text-align: center;
}

.status-dot {
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  
  &.status-completed {
    background: $status-completed;
  }
}

.empty-records {
  text-align: center;
  padding: $uni-spacing-2xl;
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: $uni-spacing-base;
  opacity: 0.3;
}

.empty-text {
  font-size: $uni-font-size-base;
  color: $uni-text-color-secondary;
}
</style>
