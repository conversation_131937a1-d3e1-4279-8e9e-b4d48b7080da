/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 - 简约紫蓝主题 */
$uni-color-primary: #4c6ef5;
$uni-color-primary-light: #a5b4fc;
$uni-color-primary-dark: #364fc7;
$uni-color-success: #51cf66;
$uni-color-warning: #ffd43b;
$uni-color-error: #ff6b6b;
$uni-color-info: #74c0fc;

/* 文字基本颜色 */
$uni-text-color: #333; // 基本色
$uni-text-color-secondary: #666; // 次要文字颜色
$uni-text-color-inverse: #fff; // 反色
$uni-text-color-grey: #999; // 辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

/* 背景颜色 */
$uni-bg-color: #fff;
$uni-bg-color-grey: #f8f8f8;
$uni-bg-color-hover: #f1f1f1; // 点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); // 遮罩颜色

/* 边框颜色 */
$uni-border-color: #c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 - 使用rpx单位适配不同屏幕 */
$uni-font-size-xs: 20rpx;   // 12px
$uni-font-size-sm: 24rpx;   // 14px
$uni-font-size-base: 28rpx; // 16px
$uni-font-size-lg: 32rpx;   // 18px
$uni-font-size-xl: 36rpx;   // 20px
$uni-font-size-2xl: 40rpx;  // 22px

/* 图片尺寸 */
$uni-img-size-sm: 20px;
$uni-img-size-base: 26px;
$uni-img-size-lg: 40px;

/* Border Radius - 现代化圆角 */
$uni-border-radius-xs: 4rpx;
$uni-border-radius-sm: 8rpx;
$uni-border-radius-base: 12rpx;
$uni-border-radius-lg: 16rpx;
$uni-border-radius-xl: 24rpx;
$uni-border-radius-circle: 50%;

/* 间距系统 */
$uni-spacing-xs: 8rpx;
$uni-spacing-sm: 12rpx;
$uni-spacing-base: 16rpx;
$uni-spacing-lg: 24rpx;
$uni-spacing-xl: 32rpx;
$uni-spacing-2xl: 48rpx;

/* 兼容旧版本 */
$uni-spacing-row-sm: $uni-spacing-sm;
$uni-spacing-row-base: $uni-spacing-base;
$uni-spacing-row-lg: $uni-spacing-lg;
$uni-spacing-col-sm: $uni-spacing-xs;
$uni-spacing-col-base: $uni-spacing-base;
$uni-spacing-col-lg: $uni-spacing-lg;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-font-size-title: 20px;
$uni-color-subtitle: #555; // 二级标题颜色
$uni-font-size-subtitle: 18px;
$uni-color-paragraph: #3f536e; // 文章段落颜色
$uni-font-size-paragraph: 15px;

/* ==================== 燃气管理系统专用样式 ==================== */

/* 简约阴影系统 */
$shadow-xs: 0 1rpx 2rpx rgba(0, 0, 0, 0.03);
$shadow-sm: 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
$shadow-base: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
$shadow-lg: 0 8rpx 16rpx rgba(0, 0, 0, 0.10);

/* 简约卡片阴影 */
$card-shadow-hover: 0 4rpx 12rpx rgba(76, 110, 245, 0.12);
$card-shadow-active: 0 2rpx 4rpx rgba(76, 110, 245, 0.15);

/* 状态颜色 */
$status-new: #FF9800;      // 新任务 - 橙色
$status-progress: #2196F3; // 进行中 - 蓝色
$status-completed: #4CAF50; // 已完成 - 绿色
$status-cancelled: #9E9E9E; // 已取消 - 灰色

/* 优先级颜色 */
$priority-low: #9E9E9E;    // 低优先级 - 灰色
$priority-normal: #2196F3; // 普通 - 蓝色
$priority-high: #FF9800;   // 高优先级 - 橙色
$priority-urgent: #F44336; // 紧急 - 红色

/* 组件尺寸 */
$navbar-height: 88rpx;
$tabbar-height: 100rpx;
$button-height-sm: 64rpx;
$button-height-base: 80rpx;
$button-height-lg: 96rpx;
$input-height: 80rpx;

/* 简约卡片样式 */
$card-bg: #ffffff;
$card-border-radius: 12rpx;
$card-shadow: $shadow-sm;
$card-padding: 24rpx;
$card-margin: 16rpx;

/* 简约色彩系统 */
$gradient-primary: linear-gradient(135deg, #4c6ef5 0%, #364fc7 100%);
$gradient-success: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
$gradient-warning: linear-gradient(135deg, #ffd43b 0%, #fcc419 100%);
$gradient-error: linear-gradient(135deg, #ff6b6b 0%, #fa5252 100%);

/* 简约背景色 */
$bg-primary: #fafbfc;
$bg-secondary: #f1f3f4;
$bg-card: #ffffff;
$bg-overlay: rgba(0, 0, 0, 0.5);

/* ==================== 全局样式类 ==================== */

/* 布局类 */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 简约卡片组件 */
.card {
  background: $card-bg;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  padding: $card-padding;
  margin-bottom: $card-margin;
  border: 1rpx solid rgba(0, 0, 0, 0.02);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: $card-shadow-hover;
    transform: translateY(-2rpx);
  }

  &:active {
    box-shadow: $card-shadow-active;
    transform: translateY(-1rpx);
  }
}

.card-elevated {
  box-shadow: $shadow-base;

  &:hover {
    box-shadow: $shadow-lg;
  }
}

.card-interactive {
  cursor: pointer;

  &:hover {
    box-shadow: $shadow-base;
    transform: translateY(-2rpx);
  }
}