<template>
  <view class="test-page">
    <view class="test-header">
      <text class="test-title">uni-icons 测试页面</text>
    </view>
    
    <view class="icon-grid">
      <view class="icon-item">
        <uni-icons type="search" size="30" color="#333"></uni-icons>
        <text>search</text>
      </view>
      
      <view class="icon-item">
        <uni-icons type="clear" size="30" color="#333"></uni-icons>
        <text>clear</text>
      </view>
      
      <view class="icon-item">
        <uni-icons type="down" size="30" color="#333"></uni-icons>
        <text>down</text>
      </view>
      
      <view class="icon-item">
        <uni-icons type="location-filled" size="30" color="#ff6b6b"></uni-icons>
        <text>location-filled</text>
      </view>
      
      <view class="icon-item">
        <uni-icons type="phone-filled" size="30" color="#52c41a"></uni-icons>
        <text>phone-filled</text>
      </view>
      
      <view class="icon-item">
        <uni-icons type="navigate-filled" size="30" color="#1890ff"></uni-icons>
        <text>navigate-filled</text>
      </view>
      
      <view class="icon-item">
        <uni-icons type="settings-filled" size="30" color="#666"></uni-icons>
        <text>settings-filled</text>
      </view>
      
      <view class="icon-item">
        <uni-icons type="list" size="30" color="#333"></uni-icons>
        <text>list</text>
      </view>
      
      <view class="icon-item">
        <uni-icons type="closeempty" size="30" color="#333"></uni-icons>
        <text>closeempty</text>
      </view>
    </view>
    
    <view class="test-info">
      <text>如果图标正常显示，说明uni-icons配置正确</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TestPage',
  data() {
    return {
      
    }
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.test-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40rpx;
  margin-bottom: 60rpx;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  
  text {
    margin-top: 16rpx;
    font-size: 24rpx;
    color: #666;
    text-align: center;
  }
}

.test-info {
  text-align: center;
  padding: 30rpx;
  background: white;
  border-radius: 16rpx;
  
  text {
    font-size: 28rpx;
    color: #666;
  }
}
</style>
