<template>
  <view class="page-container">
    <!-- 设备列表 -->
    <scroll-view class="content-scroll" scroll-y="true">
      <view class="device-list">
        <view
          class="device-card"
          v-for="cylinder in gasCylinders"
          :key="cylinder.id"
          @click="showDeviceDetail(cylinder)"
        >
          <!-- 设备头部 -->
          <view class="device-header">
            <view class="device-info">
              <view class="device-code-row">
                <uni-icons type="fire" size="16" color="#ff6b6b"></uni-icons>
                <text class="device-code">{{ cylinder.code }}</text>
                <view class="status-badge" :class="cylinder.status">
                  <text class="status-text">{{
                    getStatusText(cylinder.status)
                  }}</text>
                </view>
              </view>
              <text class="device-alarm"
                >报警器编号：{{ cylinder.alarmCode }}</text
              >
            </view>
          </view>

          <!-- 设备详情 -->
          <view class="device-details">
            <view class="detail-grid">
              <view class="detail-item">
                <text class="detail-label">充装单位</text>
                <text class="detail-value">{{ cylinder.fillingUnit }}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">充装时间</text>
                <text class="detail-value">{{ cylinder.fillingTime }}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">充装重量</text>
                <text class="detail-value">{{ cylinder.fillingWeight }}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">报警器状态</text>
                <text class="detail-value">{{ cylinder.alarmStatus }}</text>
              </view>
            </view>

            <!-- 电量显示 -->
            <view class="battery-section">
              <view class="battery-info">
                <uni-icons type="battery" size="16" color="#52c41a"></uni-icons>
                <text class="battery-label">瓶阀电量</text>
                <text
                  class="battery-value"
                  :class="getBatteryTextClass(cylinder.batteryLevel)"
                  >{{ cylinder.batteryLevel }}%</text
                >
              </view>
              <view class="battery-bar">
                <view
                  class="battery-fill"
                  :class="getBatteryClass(cylinder.batteryLevel)"
                  :style="{ width: cylinder.batteryLevel + '%' }"
                ></view>
              </view>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="device-actions">
            <button
              class="valve-btn"
              :class="{
                open: cylinder.valveStatus === 'open',
                closed: cylinder.valveStatus === 'closed',
              }"
              @click.stop="toggleValve(cylinder)"
            >
              <text class="valve-text">{{
                cylinder.valveStatus === "open" ? "关闭阀门" : "开启阀门"
              }}</text>
            </button>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-bottom"></view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { showToast, showConfirm } from "@/utils/index.js";

const taskId = ref("");
const gasCylinders = ref([
  {
    id: 1,
    code: "LPG77778888",
    alarmCode: "未设置",
    fillingUnit: "充装单位F",
    fillingTime: "2023-08-10",
    fillingWeight: "11kg",
    alarmStatus: "正常",
    valveStatus: "open", // open/closed
    batteryLevel: 92,
    status: "normal", // normal/abnormal
  },
  {
    id: 2,
    code: "LPG99990000",
    alarmCode: "未设置",
    fillingUnit: "充装单位G",
    fillingTime: "2023-08-10",
    fillingWeight: "10kg",
    alarmStatus: "正常",
    valveStatus: "closed",
    batteryLevel: 67,
    status: "normal",
  },
  {
    id: 3,
    code: "LPG88889999",
    alarmCode: "AL001234567",
    fillingUnit: "充装单位H",
    fillingTime: "2023-07-25",
    fillingWeight: "15kg",
    alarmStatus: "正常",
    valveStatus: "open",
    batteryLevel: 23,
    status: "low_battery",
  },
]);

// 生命周期钩子
onMounted(() => {
  // 可以在这里处理页面参数
});

// 方法定义
// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    normal: "正常",
    abnormal: "异常",
    low_battery: "低电量",
    offline: "离线",
  };
  return statusMap[status] || "未知";
};

// 获取电量样式类
const getBatteryClass = (level) => {
  if (level >= 60) return "high";
  if (level >= 30) return "medium";
  return "low";
};

// 获取电量文字样式类
const getBatteryTextClass = (level) => {
  if (level >= 60) return "battery-high";
  if (level >= 30) return "battery-medium";
  return "battery-low";
};
// 切换阀门状态
const toggleValve = async (cylinder) => {
  const action = cylinder.valveStatus === "open" ? "关闭" : "开启";
  const confirmed = await showConfirm(
    `确认${action}阀门？`,
    `即将${action}气瓶 ${cylinder.code} 的阀门`
  );

  if (confirmed) {
    try {
      // 这里应该调用API
      cylinder.valveStatus =
        cylinder.valveStatus === "open" ? "closed" : "open";
      showToast(`阀门${action}成功`);
    } catch (error) {
      showToast(`阀门${action}失败，请重试`);
    }
  }
};

// 显示设备详情
const showDeviceDetail = (cylinder) => {
  // 可以跳转到设备详情页面或显示弹窗
  console.log("显示设备详情:", cylinder);
};
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  width: 100vw;
  max-width: 100vw;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

// 内容滚动区域
.content-scroll {
  flex: 1;
  width: 100%;
  max-width: 100%;
  padding: 24rpx 32rpx;
  box-sizing: border-box;
  overflow-x: hidden;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.device-card {
  width: 100%;
  max-width: 100%;
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  overflow: hidden;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
  gap: 16rpx;
}

.device-info {
  flex: 1;
  min-width: 0;
}

.device-code-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.device-code {
  font-size: 30rpx;
  font-weight: 600;
  color: #262626;
}

.status-badge {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;

  &.normal {
    background: rgba(82, 196, 26, 0.1);
    color: #52c41a;
  }

  &.abnormal {
    background: rgba(255, 77, 79, 0.1);
    color: #ff4d4f;
  }

  &.low_battery {
    background: rgba(250, 173, 20, 0.1);
    color: #faad14;
  }
}

.device-alarm {
  font-size: 24rpx;
  color: #8c8c8c;
}

// 设备操作区域
.device-actions {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 24rpx 0;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 16rpx;
}

.valve-btn {
  margin: 0;
  padding: 10rpx 26rpx;
  border-radius: 8px;
  border: none;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:active::before {
    opacity: 1;
  }

  &.open {
    background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
    color: #fff;

    &:hover {
      box-shadow: 0 6rpx 20rpx rgba(255, 71, 87, 0.4);
      transform: translateY(-2rpx);
    }
  }

  &.closed {
    background: linear-gradient(135deg, #4c6ef5 0%, #6c8cff 100%);
    color: #fff;

    &:hover {
      box-shadow: 0 6rpx 20rpx rgba(76, 110, 245, 0.4);
      transform: translateY(-2rpx);
    }
  }

  &:active {
    transform: translateY(0) scale(0.98);
  }
}

.valve-text {
  color: inherit;
  font-weight: 500;
}

.device-details {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.detail-label {
  font-size: 22rpx;
  color: #8c8c8c;
}

.detail-value {
  font-size: 26rpx;
  color: #262626;
  font-weight: 500;
}

.battery-section {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.battery-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.battery-label {
  font-size: 26rpx;
  color: #262626;
  font-weight: 500;
}

.battery-value {
  font-size: 26rpx;
  font-weight: 600;
  margin-left: auto;
  transition: color 0.3s ease;

  &.battery-high {
    color: #52c41a;
  }

  &.battery-medium {
    color: #faad14;
  }

  &.battery-low {
    color: #ff4d4f;
  }
}

.battery-bar {
  height: 8rpx;
  background: #e8e8e8;
  border-radius: 4rpx;
  overflow: hidden;
}

.battery-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: all 0.3s ease;

  &.high {
    background: linear-gradient(90deg, #52c41a, #73d13d);
  }

  &.medium {
    background: linear-gradient(90deg, #faad14, #ffc53d);
  }

  &.low {
    background: linear-gradient(90deg, #ff6b6b, #ff8a8a);
  }
}

.safe-bottom {
  height: 32rpx;
}
</style>
